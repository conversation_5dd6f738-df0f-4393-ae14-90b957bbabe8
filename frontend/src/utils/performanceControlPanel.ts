/**
 * PERFORMANCE CONTROL PANEL
 * Unified interface to control all performance monitoring systems
 * Reduces overhead by allowing selective monitoring
 */

import { getPerformanceMonitor } from './performanceMonitor'
import { criticalPerformanceMonitor } from './criticalPerformanceMonitor'
import { memoryMonitor } from './memoryOptimization'
// import { performanceValidator } from './performanceValidator' // TODO: Create this file

export interface PerformanceControlConfig {
  enableCoreMonitoring: boolean
  enableCriticalMonitoring: boolean
  enableMemoryMonitoring: boolean
  enableBugMonitoring: boolean
  enableValidation: boolean
  monitoringInterval: number // in milliseconds
  logLevel: 'silent' | 'errors' | 'warnings' | 'info' | 'debug'
}

export class PerformanceControlPanel {
  private static instance: PerformanceControlPanel
  private config: PerformanceControlConfig
  private activeMonitors: Set<string> = new Set()
  private intervals: Map<string, NodeJS.Timeout> = new Map()

  private constructor() {
    this.config = this.getDefaultConfig()
    this.loadConfigFromStorage()
  }

  static getInstance(): PerformanceControlPanel {
    if (!PerformanceControlPanel.instance) {
      PerformanceControlPanel.instance = new PerformanceControlPanel()
    }
    return PerformanceControlPanel.instance
  }

  private getDefaultConfig(): PerformanceControlConfig {
    return {
      enableCoreMonitoring: false,
      enableCriticalMonitoring: false,
      enableMemoryMonitoring: false,
      enableBugMonitoring: false,
      enableValidation: false,
      monitoringInterval: 5 * 60 * 1000, // 5 minutes
      logLevel: 'warnings'
    }
  }

  private loadConfigFromStorage(): void {
    if (typeof localStorage !== 'undefined') {
      const stored = localStorage.getItem('performanceControlConfig')
      if (stored) {
        try {
          this.config = { ...this.config, ...JSON.parse(stored) }
        } catch (error) {
          console.warn('Failed to load performance config from storage')
        }
      }
    }

    // Check URL parameters for debug modes
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search)
      
      if (params.has('debug')) {
        const debugMode = params.get('debug')
        switch (debugMode) {
          case 'performance':
            this.config.enableCoreMonitoring = true
            this.config.enableCriticalMonitoring = true
            break
          case 'memory':
            this.config.enableMemoryMonitoring = true
            break
          case 'bugs':
            this.config.enableBugMonitoring = true
            break
          case 'all':
            this.enableAllMonitoring()
            break
        }
      }
    }
  }

  private saveConfigToStorage(): void {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('performanceControlConfig', JSON.stringify(this.config))
    }
  }

  /**
   * Enable all monitoring systems
   */
  enableAllMonitoring(): void {
    this.config = {
      ...this.config,
      enableCoreMonitoring: true,
      enableCriticalMonitoring: true,
      enableMemoryMonitoring: true,
      enableBugMonitoring: true,
      enableValidation: true
    }
    this.saveConfigToStorage()
    this.applyConfig()
  }

  /**
   * Disable all monitoring systems
   */
  disableAllMonitoring(): void {
    this.config = {
      ...this.config,
      enableCoreMonitoring: false,
      enableCriticalMonitoring: false,
      enableMemoryMonitoring: false,
      enableBugMonitoring: false,
      enableValidation: false
    }
    this.saveConfigToStorage()
    this.stopAllMonitoring()
  }

  /**
   * Enable lightweight monitoring (minimal overhead)
   */
  enableLightweightMonitoring(): void {
    this.config = {
      ...this.config,
      enableCoreMonitoring: false,
      enableCriticalMonitoring: true,
      enableMemoryMonitoring: false,
      enableBugMonitoring: false,
      enableValidation: false,
      monitoringInterval: 10 * 60 * 1000, // 10 minutes
      logLevel: 'errors'
    }
    this.saveConfigToStorage()
    this.applyConfig()
  }

  /**
   * Apply current configuration
   */
  applyConfig(): void {
    this.stopAllMonitoring()

    if (this.config.enableCoreMonitoring) {
      this.startCoreMonitoring()
    }

    if (this.config.enableCriticalMonitoring) {
      this.startCriticalMonitoring()
    }

    if (this.config.enableMemoryMonitoring) {
      this.startMemoryMonitoring()
    }

    if (this.config.enableValidation) {
      this.startValidationMonitoring()
    }

    this.logConfigStatus()
  }

  private startCoreMonitoring(): void {
    if (this.activeMonitors.has('core')) return

    try {
      const monitor = getPerformanceMonitor()
      this.activeMonitors.add('core')
      
      if (this.config.logLevel !== 'silent') {
        console.log('📊 Core performance monitoring started')
      }
    } catch (error) {
      console.warn('Failed to start core monitoring:', error)
    }
  }

  private startCriticalMonitoring(): void {
    if (this.activeMonitors.has('critical')) return

    try {
      criticalPerformanceMonitor.startMonitoring()
      this.activeMonitors.add('critical')
      
      if (this.config.logLevel !== 'silent') {
        console.log('🔍 Critical performance monitoring started')
      }
    } catch (error) {
      console.warn('Failed to start critical monitoring:', error)
    }
  }

  private startMemoryMonitoring(): void {
    if (this.activeMonitors.has('memory')) return

    try {
      memoryMonitor.startMonitoring(this.config.monitoringInterval)
      this.activeMonitors.add('memory')
      
      if (this.config.logLevel !== 'silent') {
        console.log('🧠 Memory monitoring started')
      }
    } catch (error) {
      console.warn('Failed to start memory monitoring:', error)
    }
  }

  private startValidationMonitoring(): void {
    if (this.activeMonitors.has('validation')) return

    const interval = setInterval(async () => {
      try {
        // TODO: Implement performance validation
        // const result = await performanceValidator.validatePerformance()
        // if (result.score < 80 && this.config.logLevel !== 'silent') {
        //   console.warn(`⚠️ Performance validation: ${result.grade} (${result.score}/100)`)
        // }
      } catch (error) {
        if (this.config.logLevel === 'debug') {
          console.warn('Performance validation failed:', error)
        }
      }
    }, this.config.monitoringInterval)

    this.intervals.set('validation', interval)
    this.activeMonitors.add('validation')
    
    if (this.config.logLevel !== 'silent') {
      console.log('✅ Performance validation monitoring started')
    }
  }

  private stopAllMonitoring(): void {
    // Stop critical monitoring
    if (this.activeMonitors.has('critical')) {
      criticalPerformanceMonitor.stopMonitoring()
      this.activeMonitors.delete('critical')
    }

    // Stop memory monitoring
    if (this.activeMonitors.has('memory')) {
      memoryMonitor.stopMonitoring()
      this.activeMonitors.delete('memory')
    }

    // Stop validation monitoring
    if (this.intervals.has('validation')) {
      clearInterval(this.intervals.get('validation')!)
      this.intervals.delete('validation')
      this.activeMonitors.delete('validation')
    }

    // Clear core monitoring flag
    this.activeMonitors.delete('core')
  }

  private logConfigStatus(): void {
    if (this.config.logLevel === 'silent') return

    const activeCount = this.activeMonitors.size
    const status = activeCount === 0 ? 'All monitoring disabled' : 
                   activeCount === 1 ? '1 monitor active' :
                   `${activeCount} monitors active`

    console.log(`⚡ Performance Control Panel: ${status}`)
    
    if (this.config.logLevel === 'debug') {
      console.log('Active monitors:', Array.from(this.activeMonitors))
      console.log('Config:', this.config)
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): PerformanceControlConfig {
    return { ...this.config }
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<PerformanceControlConfig>): void {
    this.config = { ...this.config, ...updates }
    this.saveConfigToStorage()
    this.applyConfig()
  }

  /**
   * Get monitoring status
   */
  getStatus(): { activeMonitors: string[], config: PerformanceControlConfig } {
    return {
      activeMonitors: Array.from(this.activeMonitors),
      config: this.getConfig()
    }
  }

  /**
   * Quick enable/disable for development
   */
  static enableDebugMode(): void {
    const panel = PerformanceControlPanel.getInstance()
    panel.enableLightweightMonitoring()
    console.log('🔧 Debug mode enabled with lightweight monitoring')
  }

  static disableDebugMode(): void {
    const panel = PerformanceControlPanel.getInstance()
    panel.disableAllMonitoring()
    console.log('🔇 Debug mode disabled - all monitoring stopped')
  }
}

// Export singleton instance
export const performanceControlPanel = PerformanceControlPanel.getInstance()

// Initialize based on current configuration
performanceControlPanel.applyConfig()

// Add global debug helpers
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).enablePerformanceDebug = PerformanceControlPanel.enableDebugMode
  (window as any).disablePerformanceDebug = PerformanceControlPanel.disableDebugMode
  (window as any).performanceStatus = () => performanceControlPanel.getStatus()
}
