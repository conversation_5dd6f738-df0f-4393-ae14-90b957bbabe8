/**
 * Optimized Image Component
 * Simple image component with lazy loading using native browser features
 */

import React, { useState } from 'react'

interface OptimizedImageProps {
  src: string
  alt: string
  className?: string
  width?: number
  height?: number
  sizes?: string
  priority?: boolean
  placeholder?: 'blur' | 'empty' | string
  onLoad?: () => void
  onError?: () => void
  lazy?: boolean
  responsive?: boolean
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className = '',
  width,
  height,
  sizes,
  priority = false,
  placeholder = 'empty',
  onLoad,
  onError,
  lazy = true,
  responsive = true
}) => {
  const [isLoaded, setIsLoaded] = useState(false)
  const [hasError, setHasError] = useState(false)

  const handleLoad = () => {
    setIsLoaded(true)
    onLoad?.()
  }

  const handleError = () => {
    setHasError(true)
    onError?.()
  }

  return (
    <div className={`relative ${className}`}>
      {!isLoaded && !hasError && placeholder !== 'empty' && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded" />
      )}

      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        sizes={sizes}
        loading={lazy && !priority ? 'lazy' : 'eager'}
        className={`${responsive ? 'w-full h-auto' : ''} ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
        onLoad={handleLoad}
        onError={handleError}
      />

      {hasError && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center text-gray-500">
          Failed to load image
        </div>
      )}
    </div>
  )
}

export default OptimizedImage

// Avatar component with optimized loading
export const OptimizedAvatar: React.FC<{
  src?: string
  alt: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  fallback?: string
}> = ({ 
  src, 
  alt, 
  size = 'md', 
  className = '', 
  fallback 
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  }
  
  const sizePixels = {
    sm: 32,
    md: 48,
    lg: 64,
    xl: 96
  }
  
  if (!src) {
    return (
      <div className={`${sizeClasses[size]} bg-gray-300 rounded-full flex items-center justify-center ${className}`}>
        <span className="text-gray-600 font-medium">
          {alt.charAt(0).toUpperCase()}
        </span>
      </div>
    )
  }
  
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={sizePixels[size]}
      height={sizePixels[size]}
      className={`${sizeClasses[size]} rounded-full object-cover ${className}`}
      quality="thumbnail"
      priority={size === 'sm'} // Small avatars are often above fold
      placeholder={fallback || 'empty'}
      responsive={false} // Avatars don't need responsive sizing
    />
  )
}

// Logo component with optimized loading
export const OptimizedLogo: React.FC<{
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
}> = ({ 
  src, 
  alt, 
  width = 120, 
  height = 40, 
  className = '', 
  priority = true 
}) => {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      priority={priority}
      quality="full"
      responsive={false}
      lazy={false}
    />
  )
}

// Icon component with optimized loading
export const OptimizedIcon: React.FC<{
  src: string
  alt: string
  size?: number
  className?: string
}> = ({ 
  src, 
  alt, 
  size = 24, 
  className = '' 
}) => {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      className={className}
      quality="thumbnail"
      priority={false}
      responsive={false}
      lazy={true}
    />
  )
}

export default OptimizedImage
