# Generated by Django 4.2.7 on 2025-07-22 00:57

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('ems', '0008_apikey_externalservice_webhookendpoint_webhookevent_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ComplianceControl',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('control_id', models.Char<PERSON>ield(max_length=50)),
                ('name', models.Char<PERSON>ield(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(blank=True)),
                ('control_type', models.CharField(choices=[('PREVENTIVE', 'Preventive'), ('DETECTIVE', 'Detective'), ('CORRECTIVE', 'Corrective'), ('COMPENSATING', 'Compensating')], max_length=20)),
                ('status', models.CharField(choices=[('NOT_IMPLEMENTED', 'Not Implemented'), ('PARTIALLY_IMPLEMENTED', 'Partially Implemented'), ('IMPLEMENTED', 'Implemented'), ('NEEDS_IMPROVEMENT', 'Needs Improvement'), ('NOT_APPLICABLE', 'Not Applicable')], default='NOT_IMPLEMENTED', max_length=30)),
                ('implementation_notes', models.TextField(blank=True)),
                ('evidence_required', models.JSONField(default=list, help_text='Types of evidence required')),
                ('evidence_collected', models.JSONField(default=list, help_text='Evidence collected')),
                ('risk_level', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('CRITICAL', 'Critical')], default='MEDIUM', max_length=20)),
                ('priority', models.PositiveIntegerField(default=5, help_text='Priority 1-10')),
                ('last_tested_date', models.DateField(blank=True, null=True)),
                ('test_frequency_months', models.PositiveIntegerField(default=12)),
                ('test_results', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['framework', 'priority', 'control_id'],
            },
        ),
        migrations.CreateModel(
            name='DataClassification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('name_ar', models.CharField(blank=True, max_length=100)),
                ('classification_level', models.CharField(choices=[('PUBLIC', 'Public'), ('INTERNAL', 'Internal'), ('CONFIDENTIAL', 'Confidential'), ('RESTRICTED', 'Restricted'), ('TOP_SECRET', 'Top Secret')], max_length=20)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(blank=True)),
                ('encryption_required', models.BooleanField(default=False)),
                ('access_logging_required', models.BooleanField(default=True)),
                ('approval_required_for_access', models.BooleanField(default=False)),
                ('retention_period', models.PositiveIntegerField(blank=True, null=True)),
                ('retention_unit', models.CharField(choices=[('DAYS', 'Days'), ('MONTHS', 'Months'), ('YEARS', 'Years'), ('INDEFINITE', 'Indefinite')], default='YEARS', max_length=20)),
                ('disposal_method', models.CharField(blank=True, max_length=100)),
                ('regulatory_requirements', models.JSONField(default=list, help_text='Specific regulatory requirements')),
                ('geographic_restrictions', models.JSONField(default=list, help_text='Geographic access restrictions')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['classification_level', 'name'],
            },
        ),
        migrations.CreateModel(
            name='SecurityAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_id', models.CharField(max_length=50, unique=True)),
                ('alert_type', models.CharField(choices=[('LOGIN_ANOMALY', 'Login Anomaly'), ('MULTIPLE_FAILED_LOGINS', 'Multiple Failed Logins'), ('PRIVILEGE_ESCALATION', 'Privilege Escalation'), ('DATA_ACCESS_VIOLATION', 'Data Access Violation'), ('UNUSUAL_DATA_EXPORT', 'Unusual Data Export'), ('SYSTEM_INTRUSION', 'System Intrusion'), ('MALWARE_DETECTION', 'Malware Detection'), ('POLICY_VIOLATION', 'Policy Violation'), ('COMPLIANCE_BREACH', 'Compliance Breach'), ('CUSTOM', 'Custom Alert')], max_length=30)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('severity', models.CharField(choices=[('INFO', 'Information'), ('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('CRITICAL', 'Critical')], max_length=20)),
                ('status', models.CharField(choices=[('OPEN', 'Open'), ('ACKNOWLEDGED', 'Acknowledged'), ('INVESTIGATING', 'Investigating'), ('RESOLVED', 'Resolved'), ('FALSE_POSITIVE', 'False Positive')], default='OPEN', max_length=20)),
                ('source_system', models.CharField(blank=True, max_length=100)),
                ('source_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('affected_resource', models.CharField(blank=True, max_length=200)),
                ('detection_rules', models.JSONField(default=list, help_text='Rules that triggered the alert')),
                ('evidence', models.JSONField(default=dict, help_text='Supporting evidence')),
                ('risk_score', models.PositiveIntegerField(default=0, help_text='Risk score 0-100')),
                ('response_actions', models.JSONField(default=list, help_text='Actions taken in response')),
                ('resolution_notes', models.TextField(blank=True)),
                ('detected_at', models.DateTimeField()),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('escalated', models.BooleanField(default=False)),
                ('escalated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-severity', '-detected_at'],
            },
        ),
        migrations.CreateModel(
            name='SecurityIncident',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('incident_id', models.CharField(max_length=50, unique=True)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('incident_type', models.CharField(choices=[('UNAUTHORIZED_ACCESS', 'Unauthorized Access'), ('DATA_BREACH', 'Data Breach'), ('MALWARE', 'Malware Detection'), ('PHISHING', 'Phishing Attempt'), ('BRUTE_FORCE', 'Brute Force Attack'), ('PRIVILEGE_ESCALATION', 'Privilege Escalation'), ('DATA_LOSS', 'Data Loss'), ('SYSTEM_COMPROMISE', 'System Compromise'), ('POLICY_VIOLATION', 'Policy Violation'), ('OTHER', 'Other')], max_length=30)),
                ('severity', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('CRITICAL', 'Critical')], max_length=20)),
                ('status', models.CharField(choices=[('OPEN', 'Open'), ('INVESTIGATING', 'Investigating'), ('CONTAINED', 'Contained'), ('RESOLVED', 'Resolved'), ('CLOSED', 'Closed')], default='OPEN', max_length=20)),
                ('affected_systems', models.JSONField(default=list, help_text='List of affected systems/resources')),
                ('data_categories_affected', models.JSONField(default=list, help_text='Types of data potentially affected')),
                ('detected_at', models.DateTimeField()),
                ('reported_at', models.DateTimeField(auto_now_add=True)),
                ('contained_at', models.DateTimeField(blank=True, null=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('root_cause', models.TextField(blank=True)),
                ('impact_assessment', models.TextField(blank=True)),
                ('remediation_actions', models.JSONField(default=list, help_text='List of remediation actions taken')),
                ('regulatory_notification_required', models.BooleanField(default=False)),
                ('regulatory_notification_sent', models.DateTimeField(blank=True, null=True)),
                ('customer_notification_required', models.BooleanField(default=False)),
                ('customer_notification_sent', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-severity', '-detected_at'],
            },
        ),
        migrations.CreateModel(
            name='SecurityRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('name_ar', models.CharField(blank=True, max_length=100)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('role_type', models.CharField(choices=[('SYSTEM', 'System Role'), ('BUSINESS', 'Business Role'), ('FUNCTIONAL', 'Functional Role'), ('CUSTOM', 'Custom Role')], default='CUSTOM', max_length=20)),
                ('permissions', models.JSONField(default=list, help_text='List of permission strings')),
                ('resource_access', models.JSONField(default=dict, help_text='Resource-specific access rules')),
                ('data_access_level', models.CharField(default='DEPARTMENT', help_text='Data access scope: ALL, DEPARTMENT, TEAM, SELF', max_length=50)),
                ('is_system_role', models.BooleanField(default=False, help_text='System-managed role')),
                ('is_active', models.BooleanField(default=True)),
                ('requires_approval', models.BooleanField(default=False, help_text='Role assignment requires approval')),
                ('max_concurrent_sessions', models.PositiveIntegerField(default=5)),
                ('session_timeout_minutes', models.PositiveIntegerField(default=480)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['role_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='UserSecurityProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('security_level', models.CharField(choices=[('LOW', 'Low Security'), ('MEDIUM', 'Medium Security'), ('HIGH', 'High Security'), ('CRITICAL', 'Critical Security')], default='MEDIUM', max_length=20)),
                ('mfa_enabled', models.BooleanField(default=False)),
                ('mfa_method', models.CharField(choices=[('NONE', 'No MFA'), ('SMS', 'SMS Code'), ('EMAIL', 'Email Code'), ('TOTP', 'Time-based OTP'), ('HARDWARE', 'Hardware Token')], default='NONE', max_length=20)),
                ('mfa_secret', models.CharField(blank=True, max_length=255)),
                ('password_expires_at', models.DateTimeField(blank=True, null=True)),
                ('password_history', models.JSONField(default=list, help_text='Hashed password history')),
                ('failed_login_attempts', models.PositiveIntegerField(default=0)),
                ('account_locked_until', models.DateTimeField(blank=True, null=True)),
                ('max_concurrent_sessions', models.PositiveIntegerField(default=3)),
                ('current_session_count', models.PositiveIntegerField(default=0)),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('last_login_location', models.CharField(blank=True, max_length=200)),
                ('login_notifications', models.BooleanField(default=True)),
                ('suspicious_activity_alerts', models.BooleanField(default=True)),
                ('data_access_notifications', models.BooleanField(default=False)),
                ('security_training_completed', models.DateTimeField(blank=True, null=True)),
                ('privacy_policy_accepted', models.DateTimeField(blank=True, null=True)),
                ('terms_accepted', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-security_level', 'employee__user__username'],
            },
        ),
        migrations.RemoveField(
            model_name='complianceassessment',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='complianceassessment',
            name='framework',
        ),
        migrations.RemoveField(
            model_name='complianceevidence',
            name='requirement',
        ),
        migrations.RemoveField(
            model_name='complianceevidence',
            name='uploaded_by',
        ),
        migrations.RemoveField(
            model_name='compliancerequirement',
            name='assigned_to',
        ),
        migrations.RemoveField(
            model_name='compliancerequirement',
            name='framework',
        ),
        migrations.AlterModelOptions(
            name='complianceframework',
            options={'ordering': ['framework_type', 'name']},
        ),
        migrations.RemoveField(
            model_name='audittrail',
            name='action',
        ),
        migrations.RemoveField(
            model_name='audittrail',
            name='compliance_relevant',
        ),
        migrations.RemoveField(
            model_name='audittrail',
            name='metadata',
        ),
        migrations.RemoveField(
            model_name='audittrail',
            name='resource',
        ),
        migrations.RemoveField(
            model_name='audittrail',
            name='retention_date',
        ),
        migrations.RemoveField(
            model_name='complianceframework',
            name='last_assessment',
        ),
        migrations.RemoveField(
            model_name='complianceframework',
            name='next_assessment',
        ),
        migrations.RemoveField(
            model_name='complianceframework',
            name='region',
        ),
        migrations.RemoveField(
            model_name='complianceframework',
            name='type',
        ),
        migrations.AddField(
            model_name='audittrail',
            name='action_description',
            field=models.TextField(default='System action'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='audittrail',
            name='action_type',
            field=models.CharField(choices=[('CREATE', 'Create'), ('READ', 'Read'), ('UPDATE', 'Update'), ('DELETE', 'Delete'), ('LOGIN', 'Login'), ('LOGOUT', 'Logout'), ('EXPORT', 'Export'), ('IMPORT', 'Import'), ('APPROVE', 'Approve'), ('REJECT', 'Reject'), ('CUSTOM', 'Custom Action')], default='CUSTOM', max_length=20),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='audittrail',
            name='additional_context',
            field=models.JSONField(default=dict, help_text='Additional context data'),
        ),
        migrations.AddField(
            model_name='audittrail',
            name='business_justification',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='audittrail',
            name='changed_fields',
            field=models.JSONField(default=list, help_text='List of changed field names'),
        ),
        migrations.AddField(
            model_name='audittrail',
            name='compliance_category',
            field=models.CharField(blank=True, help_text='Compliance framework category', max_length=100),
        ),
        migrations.AddField(
            model_name='audittrail',
            name='employee',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee'),
        ),
        migrations.AddField(
            model_name='audittrail',
            name='location',
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AddField(
            model_name='audittrail',
            name='resource_name',
            field=models.CharField(blank=True, help_text='Human-readable resource name', max_length=200),
        ),
        migrations.AddField(
            model_name='audittrail',
            name='resource_type',
            field=models.CharField(default='System', help_text='Model name or resource type', max_length=100),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='audittrail',
            name='risk_level',
            field=models.CharField(choices=[('LOW', 'Low Risk'), ('MEDIUM', 'Medium Risk'), ('HIGH', 'High Risk'), ('CRITICAL', 'Critical Risk')], default='LOW', max_length=20),
        ),
        migrations.AddField(
            model_name='audittrail',
            name='session_id',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='complianceframework',
            name='assessment_frequency_months',
            field=models.PositiveIntegerField(default=12),
        ),
        migrations.AddField(
            model_name='complianceframework',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee'),
        ),
        migrations.AddField(
            model_name='complianceframework',
            name='description_ar',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='complianceframework',
            name='effective_date',
            field=models.DateField(default=datetime.date(2025, 7, 22)),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='complianceframework',
            name='framework_type',
            field=models.CharField(choices=[('GDPR', 'General Data Protection Regulation'), ('SOX', 'Sarbanes-Oxley Act'), ('HIPAA', 'Health Insurance Portability and Accountability Act'), ('PCI_DSS', 'Payment Card Industry Data Security Standard'), ('ISO_27001', 'ISO/IEC 27001'), ('NIST', 'NIST Cybersecurity Framework'), ('SAUDI_DPA', 'Saudi Data Protection Act'), ('CUSTOM', 'Custom Framework')], default='CUSTOM', max_length=20),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='complianceframework',
            name='implementation_status',
            field=models.CharField(default='PLANNING', max_length=50),
        ),
        migrations.AddField(
            model_name='complianceframework',
            name='last_assessment_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='complianceframework',
            name='name_ar',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='complianceframework',
            name='next_assessment_due',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='complianceframework',
            name='requirements',
            field=models.JSONField(default=list, help_text='List of compliance requirements'),
        ),
        migrations.AlterField(
            model_name='audittrail',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='audittrail',
            name='ip_address',
            field=models.GenericIPAddressField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='audittrail',
            name='new_values',
            field=models.JSONField(blank=True, default=dict, help_text='New values'),
        ),
        migrations.AlterField(
            model_name='audittrail',
            name='old_values',
            field=models.JSONField(blank=True, default=dict, help_text='Previous values'),
        ),
        migrations.AlterField(
            model_name='audittrail',
            name='resource_id',
            field=models.CharField(blank=True, help_text='Resource identifier', max_length=100),
        ),
        migrations.AlterField(
            model_name='audittrail',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='audittrail',
            name='user_agent',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='complianceframework',
            name='compliance_score',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Compliance percentage', max_digits=5),
        ),
        migrations.AlterField(
            model_name='complianceframework',
            name='description',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='complianceframework',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='complianceframework',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name='complianceframework',
            name='name',
            field=models.CharField(max_length=100),
        ),
        migrations.AlterField(
            model_name='complianceframework',
            name='version',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AddIndex(
            model_name='audittrail',
            index=models.Index(fields=['user', 'timestamp'], name='ems_audittr_user_id_a0d99b_idx'),
        ),
        migrations.AddIndex(
            model_name='audittrail',
            index=models.Index(fields=['resource_type', 'resource_id'], name='ems_audittr_resourc_bd514c_idx'),
        ),
        migrations.AddIndex(
            model_name='audittrail',
            index=models.Index(fields=['action_type', 'risk_level'], name='ems_audittr_action__5b9683_idx'),
        ),
        migrations.AddIndex(
            model_name='audittrail',
            index=models.Index(fields=['timestamp'], name='ems_audittr_timesta_c7a2c6_idx'),
        ),
        migrations.DeleteModel(
            name='ComplianceAssessment',
        ),
        migrations.DeleteModel(
            name='ComplianceEvidence',
        ),
        migrations.DeleteModel(
            name='ComplianceRequirement',
        ),
        migrations.AddField(
            model_name='usersecurityprofile',
            name='employee',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='security_profile', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='securityrole',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee'),
        ),
        migrations.AddField(
            model_name='securityrole',
            name='parent_roles',
            field=models.ManyToManyField(blank=True, related_name='child_roles', to='ems.securityrole'),
        ),
        migrations.AddField(
            model_name='securityincident',
            name='affected_users',
            field=models.ManyToManyField(blank=True, related_name='security_incidents', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='securityincident',
            name='assigned_to',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_incidents', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='securityincident',
            name='reported_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reported_incidents', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='securityalert',
            name='affected_user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='securityalert',
            name='assigned_to',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee'),
        ),
        migrations.AddField(
            model_name='securityalert',
            name='escalated_to',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='escalated_alerts', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='dataclassification',
            name='applicable_frameworks',
            field=models.ManyToManyField(blank=True, to='ems.complianceframework'),
        ),
        migrations.AddField(
            model_name='dataclassification',
            name='authorized_roles',
            field=models.ManyToManyField(blank=True, to='ems.securityrole'),
        ),
        migrations.AddField(
            model_name='dataclassification',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee'),
        ),
        migrations.AddField(
            model_name='compliancecontrol',
            name='control_owner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='owned_controls', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='compliancecontrol',
            name='framework',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='controls', to='ems.complianceframework'),
        ),
        migrations.AddField(
            model_name='compliancecontrol',
            name='responsible_department',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.department'),
        ),
        migrations.AddIndex(
            model_name='securityalert',
            index=models.Index(fields=['alert_type', 'severity'], name='ems_securit_alert_t_1b3ec8_idx'),
        ),
        migrations.AddIndex(
            model_name='securityalert',
            index=models.Index(fields=['status', 'detected_at'], name='ems_securit_status_3aebe1_idx'),
        ),
        migrations.AddIndex(
            model_name='securityalert',
            index=models.Index(fields=['affected_user', 'detected_at'], name='ems_securit_affecte_fd7ff7_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='compliancecontrol',
            unique_together={('framework', 'control_id')},
        ),
    ]
