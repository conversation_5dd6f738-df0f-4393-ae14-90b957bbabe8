from django.db.models.signals import post_save, post_delete, pre_save
from django.contrib.auth.signals import user_logged_in, user_logged_out, user_login_failed
from django.dispatch import receiver
from django.contrib.auth.models import User
from django.utils import timezone
from threading import local
import json

from .services import <PERSON>t<PERSON>ogger
from .models import AuditAction, AuditSeverity

# Thread-local storage for request context
_thread_locals = local()

def set_current_request(request):
    """Set the current request in thread-local storage"""
    _thread_locals.request = request

def get_current_request():
    """Get the current request from thread-local storage"""
    return getattr(_thread_locals, 'request', None)

def set_current_user(user):
    """Set the current user in thread-local storage"""
    _thread_locals.user = user

def get_current_user():
    """Get the current user from thread-local storage"""
    return getattr(_thread_locals, 'user', None)

# Store original values for change tracking
_original_values = {}

@receiver(pre_save)
def store_original_values(sender, instance, **kwargs):
    """Store original values before save for change tracking"""
    if instance.pk:
        try:
            original = sender.objects.get(pk=instance.pk)
            _original_values[f"{sender.__name__}_{instance.pk}"] = original
        except sender.DoesNotExist:
            pass

@receiver(post_save)
def log_model_save(sender, instance, created, **kwargs):
    """Log model save operations"""

    # Skip logging during migrations
    import sys
    if 'migrate' in sys.argv:
        return

    # Skip audit log models to prevent infinite recursion
    if sender.__name__ in ['AuditLog', 'SecurityEvent', 'AuditConfiguration']:
        return

    user = get_current_user()
    request = get_current_request()

    if created:
        # Log creation
        AuditLogger.log_action(
            user=user,
            action=AuditAction.CREATE,
            content_object=instance,
            request=request,
            additional_data={
                'model': sender.__name__,
                'created_at': timezone.now().isoformat()
            },
            severity=AuditSeverity.LOW
        )
    else:
        # Log update with changes
        changes = {}
        original_key = f"{sender.__name__}_{instance.pk}"
        
        if original_key in _original_values:
            original = _original_values[original_key]
            
            # Compare fields to find changes
            for field in instance._meta.fields:
                field_name = field.name
                old_value = getattr(original, field_name, None)
                new_value = getattr(instance, field_name, None)
                
                if old_value != new_value:
                    # Convert to string for JSON serialization
                    changes[field_name] = {
                        'old': str(old_value) if old_value is not None else None,
                        'new': str(new_value) if new_value is not None else None
                    }
            
            # Clean up stored original value
            del _original_values[original_key]
        
        if changes:  # Only log if there are actual changes
            AuditLogger.log_action(
                user=user,
                action=AuditAction.UPDATE,
                content_object=instance,
                request=request,
                changes=changes,
                additional_data={
                    'model': sender.__name__,
                    'updated_at': timezone.now().isoformat()
                },
                severity=AuditSeverity.LOW
            )

@receiver(post_delete)
def log_model_delete(sender, instance, **kwargs):
    """Log model delete operations"""

    # Skip logging during migrations
    import sys
    if 'migrate' in sys.argv:
        return

    # Skip audit log models
    if sender.__name__ in ['AuditLog', 'SecurityEvent', 'AuditConfiguration']:
        return
    
    user = get_current_user()
    request = get_current_request()
    
    AuditLogger.log_action(
        user=user,
        action=AuditAction.DELETE,
        content_object=None,  # Object is already deleted
        request=request,
        additional_data={
            'model': sender.__name__,
            'deleted_object_repr': str(instance),
            'deleted_object_id': str(instance.pk) if hasattr(instance, 'pk') else '',
            'deleted_at': timezone.now().isoformat()
        },
        severity=AuditSeverity.MEDIUM
    )

@receiver(user_logged_in)
def log_user_login(sender, request, user, **kwargs):
    """Log successful user login"""
    AuditLogger.log_login_attempt(
        user=user,
        request=request,
        success=True
    )

@receiver(user_logged_out)
def log_user_logout(sender, request, user, **kwargs):
    """Log user logout"""
    AuditLogger.log_action(
        user=user,
        action=AuditAction.LOGOUT,
        request=request,
        additional_data={
            'logout_time': timezone.now().isoformat()
        },
        severity=AuditSeverity.LOW,
        compliance_tags=['authentication']
    )

@receiver(user_login_failed)
def log_user_login_failed(sender, credentials, request, **kwargs):
    """Log failed login attempts"""
    username = credentials.get('username', 'Unknown')
    
    # Try to get user object if it exists
    user = None
    try:
        user = User.objects.get(username=username)
    except User.DoesNotExist:
        pass
    
    AuditLogger.log_login_attempt(
        user=user,
        request=request,
        success=False,
        error_message=f"Failed login attempt for username: {username}"
    )
    
    # Check for potential brute force attack
    from .services import AuditLogger as AL
    from .models import AuditConfiguration
    AL._check_security_events(
        AL.log_action(
            user=user,
            action=AuditAction.LOGIN_FAILED,
            request=request,
            additional_data={'attempted_username': username},
            severity=AuditSeverity.MEDIUM,
            success=False
        ),
        AuditConfiguration.get_config()
    )

# Custom signals for specific events
from django.dispatch import Signal

# Define custom signals
permission_denied = Signal()
data_exported = Signal()
bulk_operation_performed = Signal()
security_event_detected = Signal()

@receiver(permission_denied)
def log_permission_denied(sender, user, request, resource, **kwargs):
    """Log permission denied events"""
    AuditLogger.log_action(
        user=user,
        action=AuditAction.PERMISSION_DENIED,
        request=request,
        additional_data={
            'resource': resource,
            'attempted_action': kwargs.get('action', 'unknown')
        },
        severity=AuditSeverity.MEDIUM,
        success=False,
        error_message=f"Permission denied for resource: {resource}",
        compliance_tags=['security', 'access_control']
    )

@receiver(data_exported)
def log_data_export(sender, user, request, export_type, record_count, **kwargs):
    """Log data export events"""
    AuditLogger.log_action(
        user=user,
        action=AuditAction.EXPORT,
        request=request,
        additional_data={
            'export_type': export_type,
            'record_count': record_count,
            'export_format': kwargs.get('format', 'unknown'),
            'exported_at': timezone.now().isoformat()
        },
        severity=AuditSeverity.MEDIUM,
        compliance_tags=['data_export', 'privacy']
    )

@receiver(bulk_operation_performed)
def log_bulk_operation(sender, user, request, operation, model, count, **kwargs):
    """Log bulk operations"""
    AuditLogger.log_bulk_operation(
        user=user,
        action=operation,
        model_class=model,
        count=count,
        request=request
    )

@receiver(security_event_detected)
def log_security_event(sender, user, request, event_type, risk_score, **kwargs):
    """Log security events"""
    AuditLogger.log_security_event(
        user=user,
        event_type=event_type,
        request=request,
        risk_score=risk_score,
        additional_data=kwargs.get('additional_data', {})
    )
